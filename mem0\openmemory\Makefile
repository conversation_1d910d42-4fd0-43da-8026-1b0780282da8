.PHONY: help up down logs shell migrate test test-clean env ui-install ui-start ui-dev ui-build ui-dev-start up-windows

# Default environment variables
NEXT_PUBLIC_USER_ID ?= $(if $(USER),$(USER),$(USERNAME))
NEXT_PUBLIC_API_URL ?= http://localhost:8765

# Default target
help:
	@echo "Available commands:"
	@echo "  make env       - Copy .env.example to .env"
	@echo "  make up        - Start the containers (Unix/Linux/Mac)"
	@echo "  make up-windows - Start the containers (Windows)"
	@echo "  make down      - Stop the containers"
	@echo "  make logs      - Show container logs"
	@echo "  make shell     - Open a shell in the api container"
	@echo "  make migrate   - Run database migrations"
	@echo "  make test      - Run tests in a new container"
	@echo "  make test-clean - Run tests and clean up volumes"
	@echo "  make ui-install - Install frontend dependencies"
	@echo "  make ui-start  - Start the frontend development server"
	@echo "  make ui-dev    - Install dependencies and start the frontend in dev mode (Unix/Linux/Mac)"
	@echo "  make ui-dev-windows - Install dependencies and start the frontend in dev mode (Windows)"
	@echo "  make ui        - Install dependencies and start the frontend in production mode"
	@echo ""
	@echo "Windows users: Use 'make up-windows' and 'make ui-dev-windows' instead of 'make up' and 'make ui-dev'"

env:
	cd api && cp .env.example .env
	cd ui && cp .env.example .env

build:
	docker compose build

up:
	NEXT_PUBLIC_USER_ID=$(NEXT_PUBLIC_USER_ID) NEXT_PUBLIC_API_URL=$(NEXT_PUBLIC_API_URL) docker compose up

up-windows:
	@echo "Starting containers on Windows..."
	@echo "Setting environment variables:"
	@echo "  NEXT_PUBLIC_USER_ID=$(if $(USERNAME),$(USERNAME),defaultuser)"
	@echo "  NEXT_PUBLIC_API_URL=$(NEXT_PUBLIC_API_URL)"
	@set NEXT_PUBLIC_USER_ID=$(if $(USERNAME),$(USERNAME),defaultuser) && set NEXT_PUBLIC_API_URL=$(NEXT_PUBLIC_API_URL) && set USER=$(if $(USERNAME),$(USERNAME),defaultuser) && docker compose up

down:
	docker compose down -v
	rm -f api/openmemory.db

logs:
	docker compose logs -f

shell:
	docker compose exec api bash

upgrade:
	docker compose exec api alembic upgrade head

migrate:
	docker compose exec api alembic upgrade head

downgrade:
	docker compose exec api alembic downgrade -1

ui-dev:
	cd ui && NEXT_PUBLIC_USER_ID=$(NEXT_PUBLIC_USER_ID) NEXT_PUBLIC_API_URL=$(NEXT_PUBLIC_API_URL) pnpm install && pnpm dev

ui-dev-windows:
	@echo "Starting UI development server on Windows..."
	@set NEXT_PUBLIC_USER_ID=$(if $(USERNAME),$(USERNAME),defaultuser) && set NEXT_PUBLIC_API_URL=$(NEXT_PUBLIC_API_URL) && cd ui && pnpm install && pnpm dev
