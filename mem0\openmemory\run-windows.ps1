# OpenMemory Windows PowerShell Runner
# This script provides Windows-compatible commands for running OpenMemory

param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [string]$UserId = $env:USERNAME,
    [string]$ApiUrl = "http://localhost:8765"
)

# Set environment variables
$env:NEXT_PUBLIC_USER_ID = $UserId
$env:NEXT_PUBLIC_API_URL = $ApiUrl
$env:USER = $UserId

function Show-Help {
    Write-Host "OpenMemory Windows Runner - Available commands:" -ForegroundColor Green
    Write-Host ""
    Write-Host "  .\run-windows.ps1 help        - Show this help message" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 build       - Build the containers" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 up          - Start the containers" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 down        - Stop the containers" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 logs        - Show container logs" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 shell       - Open a shell in the api container" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 migrate     - Run database migrations" -ForegroundColor Yellow
    Write-Host "  .\run-windows.ps1 ui-dev      - Start the frontend in development mode" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Green
    Write-Host "  -UserId <string>   - Set the user ID (default: $env:USERNAME)" -ForegroundColor Cyan
    Write-Host "  -ApiUrl <string>   - Set the API URL (default: http://localhost:8765)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Green
    Write-Host "  .\run-windows.ps1 up" -ForegroundColor White
    Write-Host "  .\run-windows.ps1 up -UserId 'myuser' -ApiUrl 'http://localhost:8765'" -ForegroundColor White
}

function Build-Containers {
    Write-Host "Building containers..." -ForegroundColor Green
    docker compose build
}

function Start-Containers {
    Write-Host "Starting containers with:" -ForegroundColor Green
    Write-Host "  User ID: $env:NEXT_PUBLIC_USER_ID" -ForegroundColor Cyan
    Write-Host "  API URL: $env:NEXT_PUBLIC_API_URL" -ForegroundColor Cyan
    Write-Host ""
    docker compose up
}

function Stop-Containers {
    Write-Host "Stopping containers..." -ForegroundColor Green
    docker compose down -v
    if (Test-Path "api/openmemory.db") {
        Remove-Item "api/openmemory.db" -Force
        Write-Host "Removed database file" -ForegroundColor Yellow
    }
}

function Show-Logs {
    Write-Host "Showing container logs..." -ForegroundColor Green
    docker compose logs -f
}

function Open-Shell {
    Write-Host "Opening shell in API container..." -ForegroundColor Green
    docker compose exec api bash
}

function Run-Migrations {
    Write-Host "Running database migrations..." -ForegroundColor Green
    docker compose exec api alembic upgrade head
}

function Start-UIDev {
    Write-Host "Starting UI in development mode..." -ForegroundColor Green
    Write-Host "  User ID: $env:NEXT_PUBLIC_USER_ID" -ForegroundColor Cyan
    Write-Host "  API URL: $env:NEXT_PUBLIC_API_URL" -ForegroundColor Cyan
    Set-Location ui
    pnpm install
    pnpm dev
    Set-Location ..
}

# Main command dispatcher
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "build" { Build-Containers }
    "up" { Start-Containers }
    "down" { Stop-Containers }
    "logs" { Show-Logs }
    "shell" { Open-Shell }
    "migrate" { Run-Migrations }
    "ui-dev" { Start-UIDev }
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Write-Host ""
        Show-Help
    }
}
