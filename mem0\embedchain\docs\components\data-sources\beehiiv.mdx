---
title: "🐝 Beehiiv"
---

To add any Beehiiv data sources to your app, just add the base url as the source and set the data_type to `beehiiv`.

```python
from embedchain import App

app = App()

# source: just add the base url and set the data_type to 'beehiiv'
app.add('https://aibreakfast.beehiiv.com', data_type='beehiiv')
app.query("How much is OpenAI paying developers?")
# Answer: OpenAI is aggressively recruiting Google's top AI researchers with offers ranging between $5 to $10 million annually, primarily in stock options.
```
