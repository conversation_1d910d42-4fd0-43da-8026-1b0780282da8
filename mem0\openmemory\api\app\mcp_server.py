"""
MCP Server for OpenMemory with resilient memory client handling.

This module implements an MCP (Model Context Protocol) server that provides
memory operations for OpenMemory. The memory client is initialized lazily
to prevent server crashes when external dependencies (like Ollama) are
unavailable. If the memory client cannot be initialized, the server will
continue running with limited functionality and appropriate error messages.

Key features:
- Lazy memory client initialization
- Graceful error handling for unavailable dependencies
- Fallback to database-only mode when vector store is unavailable
- Proper logging for debugging connection issues
- Environment variable parsing for API keys
"""

import logging
import json
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from app.utils.memory import get_memory_client
from fastapi import FastAPI, Request
from fastapi.routing import APIRouter
import contextvars
import os
from dotenv import load_dotenv
from app.database import SessionLocal
from app.models import Memory, MemoryState, MemoryStatusHistory, MemoryAccessLog
from app.utils.db import get_user_and_app
import uuid
import datetime
from app.utils.permissions import check_memory_access_permissions
from qdrant_client import models as qdrant_models

# Load environment variables
load_dotenv()

# Initialize MCP
mcp = FastMCP("mem0-mcp-server")

# Don't initialize memory client at import time - do it lazily when needed
def get_memory_client_safe():
    """Get memory client with error handling. Returns None if client cannot be initialized."""
    try:
        return get_memory_client()
    except Exception as e:
        logging.warning(f"Failed to get memory client: {e}")
        return None

# Context variables for user_id and client_name
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar("user_id")
client_name_var: contextvars.ContextVar[str] = contextvars.ContextVar("client_name")

# Create a router for MCP endpoints
mcp_router = APIRouter(prefix="/mcp")

# Initialize SSE transport
sse = SseServerTransport("/mcp/messages/")

def get_normalized_user_and_client() -> tuple[str, str]:
    """
    Extract and normalize user_id and client_name from context variables.
    Returns a tuple of (user_id, client_name) with proper normalization.
    """
    try:
        # Extract from context variables
        context_uid = user_id_var.get(None)
        context_client = client_name_var.get(None)

        # Apply fallbacks
        uid = context_uid if context_uid and context_uid.strip() else "User"
        client_name = context_client if context_client and context_client.strip() else "cursor"

        # Normalize user ID (case-insensitive)
        uid = uid.strip()
        if uid.lower() == "user":
            uid = "User"  # Normalize to consistent case

        # Normalize client name (lowercase)
        client_name = client_name.strip().lower()

        logging.info(f"🔧 Normalized context - uid: '{uid}', client_name: '{client_name}'")
        return uid, client_name

    except Exception as e:
        logging.warning(f"🔧 Error in get_normalized_user_and_client: {e}")
        return "User", "cursor"  # Safe fallback

@mcp.tool(description="Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevant information which can be useful in the future conversation. This can also be called when the user asks you to remember something.")
async def add_memories(text: str) -> str:
    """
    Add a new memory with comprehensive error handling and logging.
    This function is designed to be robust and provide clear feedback.
    """
    # Top-level exception handler to catch any unexpected errors
    try:
        logging.info(f"🚀 MCP add_memories FUNCTION ENTRY - text: '{text}'")

        # Extract and normalize user_id and client_name using utility function
        uid, client_name = get_normalized_user_and_client()

        # Validate inputs
        if not uid or not uid.strip():
            error_msg = "🔧 MCP Error: user_id is empty or invalid"
            logging.error(error_msg)
            return f"Error: {error_msg}"
        if not client_name or not client_name.strip():
            error_msg = "🔧 MCP Error: client_name is empty or invalid"
            logging.error(error_msg)
            return f"Error: {error_msg}"
        if not text or not text.strip():
            error_msg = "🔧 MCP Error: text is empty or invalid"
            logging.error(error_msg)
            return f"Error: {error_msg}"

        logging.info(f"🔧 MCP validation passed - proceeding with uid: '{uid}', client_name: '{client_name}', text: '{text[:50]}...'")
    except Exception as e:
        error_msg = f"🔧 MCP CRITICAL ERROR in initialization: {str(e)}"
        logging.exception(error_msg)
        return f"Error: {error_msg}"

    # Get memory client safely
    try:
        logging.info(f"🔧 MCP Getting memory client...")
        memory_client = get_memory_client_safe()
        if not memory_client:
            error_msg = "🔧 MCP Error: Memory system is currently unavailable"
            logging.error(error_msg)
            return f"Error: Memory system is currently unavailable. Please try again later."
        logging.info(f"🔧 MCP Memory client obtained successfully")
    except Exception as e:
        error_msg = f"🔧 MCP Error getting memory client: {str(e)}"
        logging.exception(error_msg)
        return f"Error: {error_msg}"

    # Database operations with comprehensive error handling
    db = None
    try:
        logging.info(f"🔧 MCP Creating database session...")
        db = SessionLocal()
        logging.info(f"🔧 MCP Database session created successfully")

        # Get or create user and app
        logging.info(f"🔧 MCP Getting user and app for uid: '{uid}', client_name: '{client_name}'")
        user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
        logging.info(f"🔧 MCP Got user: '{user.user_id}', app: '{app.name}', app_active: {app.is_active}")

        # Check if app is active
        if not app.is_active:
            error_msg = f"🔧 MCP Error: App '{app.name}' is paused"
            logging.error(error_msg)
            return f"Error: App '{app.name}' is currently paused on OpenMemory. Cannot create new memories."

        # Call memory client to add memory
        logging.info(f"🔧 MCP Calling memory_client.add with text: '{text[:100]}...'")
        response = memory_client.add(text,
                                     user_id=uid,
                                     metadata={
                                        "source_app": "openmemory",
                                        "mcp_client": client_name,
                                    })
        logging.info(f"🔧 MCP Memory client response: {response}")

        # Process the response and update database
        if isinstance(response, dict) and 'results' in response:
            logging.info(f"🔧 MCP Processing {len(response['results'])} results...")
            for i, result in enumerate(response['results']):
                logging.info(f"🔧 MCP Processing result {i+1}: {result}")
                memory_id = uuid.UUID(result['id'])
                memory = db.query(Memory).filter(Memory.id == memory_id).first()

                if result['event'] == 'ADD':
                    if not memory:
                        memory = Memory(
                            id=memory_id,
                            user_id=user.id,
                            app_id=app.id,
                            content=result['memory'],
                            state=MemoryState.active
                        )
                        db.add(memory)
                        logging.info(f"🔧 MCP Created new memory: {memory_id}")
                    else:
                        memory.state = MemoryState.active
                        memory.content = result['memory']
                        logging.info(f"🔧 MCP Updated existing memory: {memory_id}")

                    # Create history entry
                    history = MemoryStatusHistory(
                        memory_id=memory_id,
                        changed_by=user.id,
                        old_state=MemoryState.deleted if memory else None,
                        new_state=MemoryState.active
                    )
                    db.add(history)

                elif result['event'] == 'DELETE':
                    if memory:
                        memory.state = MemoryState.deleted
                        memory.deleted_at = datetime.datetime.now(datetime.UTC)
                        # Create history entry
                        history = MemoryStatusHistory(
                            memory_id=memory_id,
                            changed_by=user.id,
                            old_state=MemoryState.active,
                            new_state=MemoryState.deleted
                        )
                        db.add(history)
                        logging.info(f"🔧 MCP Deleted memory: {memory_id}")

            logging.info(f"🔧 MCP Committing database changes...")
            db.commit()
            logging.info(f"🔧 MCP Database changes committed successfully")
        else:
            logging.warning(f"🔧 MCP Unexpected response format: {response}")

        logging.info(f"🔧 MCP add_memories COMPLETED successfully")
        return f"✅ Memory added successfully: {response}"

    except Exception as e:
        error_msg = f"🔧 MCP Error in database operations: {str(e)}"
        logging.exception(error_msg)
        if db:
            try:
                db.rollback()
                logging.info(f"🔧 MCP Database rollback completed")
            except Exception as rollback_error:
                logging.error(f"🔧 MCP Error during rollback: {rollback_error}")
        return f"Error: {error_msg}"
    finally:
        if db:
            try:
                db.close()
                logging.info(f"🔧 MCP Database session closed")
            except Exception as close_error:
                logging.error(f"🔧 MCP Error closing database: {close_error}")


@mcp.tool(description="Test the MCP connection and verify the server is working properly.")
async def test_connection() -> str:
    """Simple test function to verify MCP is working"""
    try:
        logging.info(f"🧪 MCP test_connection called")
        return "✅ MCP connection is working! Server is responding correctly."
    except Exception as e:
        logging.exception(f"🧪 MCP test_connection error: {e}")
        return f"❌ MCP test failed: {e}"


@mcp.tool(description="Search through stored memories. This method is called EVERYTIME the user asks anything.")
async def search_memory(query: str) -> str:
    """Search memories with dynamic user ID and client name handling"""
    try:
        logging.info(f"🔍 MCP search_memory called with query: '{query}'")

        # Extract and normalize user_id and client_name
        uid = user_id_var.get(None)
        client_name = client_name_var.get(None)

        # Fallback values
        if not uid:
            uid = "User"
            logging.warning(f"🔍 MCP using fallback uid: '{uid}'")
        if not client_name:
            client_name = "cursor"
            logging.warning(f"🔍 MCP using fallback client_name: '{client_name}'")

        # Normalize values
        uid = uid.strip()
        if uid.lower() == "user":
            uid = "User"
        client_name = client_name.strip().lower()

        logging.info(f"🔍 MCP normalized values - uid: '{uid}', client_name: '{client_name}'")

        if not uid:
            return "Error: user_id not provided"
        if not client_name:
            return "Error: client_name not provided"
    except Exception as e:
        logging.exception(f"🔍 MCP search_memory initialization error: {e}")
        return f"Error: {e}"

    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get accessible memory IDs based on ACL
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]

            conditions = [qdrant_models.FieldCondition(key="user_id", match=qdrant_models.MatchValue(value=uid))]

            if accessible_memory_ids:
                # Convert UUIDs to strings for Qdrant
                accessible_memory_ids_str = [str(memory_id) for memory_id in accessible_memory_ids]
                conditions.append(qdrant_models.HasIdCondition(has_id=accessible_memory_ids_str))

            filters = qdrant_models.Filter(must=conditions)
            embeddings = memory_client.embedding_model.embed(query, "search")

            hits = memory_client.vector_store.client.query_points(
                collection_name=memory_client.vector_store.collection_name,
                query=embeddings,
                query_filter=filters,
                limit=10,
            )

            # Process search results
            memories = hits.points
            memories = [
                {
                    "id": memory.id,
                    "memory": memory.payload["data"],
                    "hash": memory.payload.get("hash"),
                    "created_at": memory.payload.get("created_at"),
                    "updated_at": memory.payload.get("updated_at"),
                    "score": memory.score,
                }
                for memory in memories
            ]

            # Log memory access for each memory found
            if isinstance(memories, dict) and 'results' in memories:
                print(f"Memories: {memories}")
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="search",
                            metadata_={
                                "query": query,
                                "score": memory_data.get('score'),
                                "hash": memory_data.get('hash')
                            }
                        )
                        db.add(access_log)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    # Create access log entry
                    access_log = MemoryAccessLog(
                        memory_id=memory_id,
                        app_id=app.id,
                        access_type="search",
                        metadata_={
                            "query": query,
                            "score": memory.get('score'),
                            "hash": memory.get('hash')
                        }
                    )
                    db.add(access_log)
                db.commit()
            return json.dumps(memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(e)
        return f"Error searching memory: {e}"


@mcp.tool(description="List all memories in the user's memory")
async def list_memories() -> str:
    """List memories with dynamic user ID and client name handling"""
    try:
        logging.info(f"📋 MCP list_memories called")

        # Extract and normalize user_id and client_name
        uid = user_id_var.get(None)
        client_name = client_name_var.get(None)

        # Fallback values
        if not uid:
            uid = "User"
            logging.warning(f"📋 MCP using fallback uid: '{uid}'")
        if not client_name:
            client_name = "cursor"
            logging.warning(f"📋 MCP using fallback client_name: '{client_name}'")

        # Normalize values
        uid = uid.strip()
        if uid.lower() == "user":
            uid = "User"
        client_name = client_name.strip().lower()

        logging.info(f"📋 MCP normalized values - uid: '{uid}', client_name: '{client_name}'")

        if not uid:
            return "Error: user_id not provided"
        if not client_name:
            return "Error: client_name not provided"
    except Exception as e:
        logging.exception(f"📋 MCP list_memories initialization error: {e}")
        return f"Error: {e}"

    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            # Get all memories
            memories = memory_client.get_all(user_id=uid)
            filtered_memories = []

            # Filter memories based on permissions
            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]
            if isinstance(memories, dict) and 'results' in memories:
                for memory_data in memories['results']:
                    if 'id' in memory_data:
                        memory_id = uuid.UUID(memory_data['id'])
                        if memory_id in accessible_memory_ids:
                            # Create access log entry
                            access_log = MemoryAccessLog(
                                memory_id=memory_id,
                                app_id=app.id,
                                access_type="list",
                                metadata_={
                                    "hash": memory_data.get('hash')
                                }
                            )
                            db.add(access_log)
                            filtered_memories.append(memory_data)
                db.commit()
            else:
                for memory in memories:
                    memory_id = uuid.UUID(memory['id'])
                    memory_obj = db.query(Memory).filter(Memory.id == memory_id).first()
                    if memory_obj and check_memory_access_permissions(db, memory_obj, app.id):
                        # Create access log entry
                        access_log = MemoryAccessLog(
                            memory_id=memory_id,
                            app_id=app.id,
                            access_type="list",
                            metadata_={
                                "hash": memory.get('hash')
                            }
                        )
                        db.add(access_log)
                        filtered_memories.append(memory)
                db.commit()
            return json.dumps(filtered_memories, indent=2)
        finally:
            db.close()
    except Exception as e:
        logging.exception(f"Error getting memories: {e}")
        return f"Error getting memories: {e}"


@mcp.tool(description="Delete all memories in the user's memory")
async def delete_all_memories() -> str:
    """Delete all memories with dynamic user ID and client name handling"""
    try:
        logging.info(f"🗑️ MCP delete_all_memories called")

        # Extract and normalize user_id and client_name
        uid = user_id_var.get(None)
        client_name = client_name_var.get(None)

        # Fallback values
        if not uid:
            uid = "User"
            logging.warning(f"🗑️ MCP using fallback uid: '{uid}'")
        if not client_name:
            client_name = "cursor"
            logging.warning(f"🗑️ MCP using fallback client_name: '{client_name}'")

        # Normalize values
        uid = uid.strip()
        if uid.lower() == "user":
            uid = "User"
        client_name = client_name.strip().lower()

        logging.info(f"🗑️ MCP normalized values - uid: '{uid}', client_name: '{client_name}'")

        if not uid:
            return "Error: user_id not provided"
        if not client_name:
            return "Error: client_name not provided"
    except Exception as e:
        logging.exception(f"🗑️ MCP delete_all_memories initialization error: {e}")
        return f"Error: {e}"

    # Get memory client safely
    memory_client = get_memory_client_safe()
    if not memory_client:
        return "Error: Memory system is currently unavailable. Please try again later."

    try:
        db = SessionLocal()
        try:
            # Get or create user and app
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)

            user_memories = db.query(Memory).filter(Memory.user_id == user.id).all()
            accessible_memory_ids = [memory.id for memory in user_memories if check_memory_access_permissions(db, memory, app.id)]

            # delete the accessible memories only
            for memory_id in accessible_memory_ids:
                try:
                    memory_client.delete(memory_id)
                except Exception as delete_error:
                    logging.warning(f"Failed to delete memory {memory_id} from vector store: {delete_error}")

            # Update each memory's state and create history entries
            now = datetime.datetime.now(datetime.UTC)
            for memory_id in accessible_memory_ids:
                memory = db.query(Memory).filter(Memory.id == memory_id).first()
                # Update memory state
                memory.state = MemoryState.deleted
                memory.deleted_at = now

                # Create history entry
                history = MemoryStatusHistory(
                    memory_id=memory_id,
                    changed_by=user.id,
                    old_state=MemoryState.active,
                    new_state=MemoryState.deleted
                )
                db.add(history)

                # Create access log entry
                access_log = MemoryAccessLog(
                    memory_id=memory_id,
                    app_id=app.id,
                    access_type="delete_all",
                    metadata_={"operation": "bulk_delete"}
                )
                db.add(access_log)

            db.commit()
            return "Successfully deleted all memories"
        finally:
            db.close()
    except Exception as e:
        logging.exception(f"Error deleting memories: {e}")
        return f"Error deleting memories: {e}"


@mcp_router.get("/{client_name}/sse/{user_id}")
async def handle_sse(request: Request):
    """Handle SSE connections for a specific user and client"""
    # Extract user_id and client_name from path parameters
    uid = request.path_params.get("user_id")
    client_name = request.path_params.get("client_name")

    # Normalize values
    if uid:
        uid = uid.strip()
        if uid.lower() == "user":
            uid = "User"  # Normalize to consistent case
    if client_name:
        client_name = client_name.strip().lower()

    logging.info(f"🌐 SSE Connection established - uid: '{uid}', client_name: '{client_name}'")

    # Set context variables with normalized values
    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name or "")

    logging.info(f"🌐 Context variables set - uid_token: {user_token}, client_token: {client_token}")

    try:
        # Handle SSE connection
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            await mcp._mcp_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    finally:
        # Clean up context variables
        logging.info(f"🌐 Cleaning up context variables for uid: '{uid}', client_name: '{client_name}'")
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        logging.info(f"🌐 Context variables cleaned up successfully")


@mcp_router.post("/messages/")
async def handle_get_message(request: Request):
    return await handle_post_message(request)


@mcp_router.post("/{client_name}/sse/{user_id}/messages/")
async def handle_post_message(request: Request):
    return await handle_post_message(request)

async def handle_post_message(request: Request):
    """Handle POST messages for SSE"""
    try:
        body = await request.body()

        # Create a simple receive function that returns the body
        async def receive():
            return {"type": "http.request", "body": body, "more_body": False}

        # Create a simple send function that does nothing
        async def send(message):
            return {}

        # Call handle_post_message with the correct arguments
        await sse.handle_post_message(request.scope, receive, send)

        # Return a success response
        return {"status": "ok"}
    finally:
        pass
        # Clean up context variable
        # client_name_var.reset(client_token)

def setup_mcp_server(app: FastAPI):
    """Setup MCP server with the FastAPI application"""
    mcp._mcp_server.name = f"mem0-mcp-server"

    # Include MCP router in the FastAPI app
    app.include_router(mcp_router)
