---
title: 📄 get
---

## Get data sources

`get_data_sources()` returns a list of all the data sources added in the app.


### Usage

```python
from embedchain import App

app = App()

app.add("https://www.forbes.com/profile/elon-musk")
app.add("https://en.wikipedia.org/wiki/Elon_Musk")

data_sources = app.get_data_sources()
# [
#   {
#       'data_type': 'web_page',
#       'data_value': 'https://en.wikipedia.org/wiki/Elon_Musk',
#       'metadata': 'null'
#   },
#   {
#       'data_type': 'web_page',
#       'data_value': 'https://www.forbes.com/profile/elon-musk',
#       'metadata': 'null'
#   }
# ]
```