@echo off
REM OpenMemory Windows Batch Runner
REM This script provides Windows-compatible commands for running OpenMemory

setlocal enabledelayedexpansion

REM Set default values
set "USER_ID=%USERNAME%"
set "API_URL=http://localhost:8765"

REM Parse command line arguments
set "COMMAND=%1"
if "%COMMAND%"=="" set "COMMAND=help"

REM Set environment variables
set "NEXT_PUBLIC_USER_ID=%USER_ID%"
set "NEXT_PUBLIC_API_URL=%API_URL%"
set "USER=%USER_ID%"

if /i "%COMMAND%"=="help" goto :help
if /i "%COMMAND%"=="build" goto :build
if /i "%COMMAND%"=="up" goto :up
if /i "%COMMAND%"=="down" goto :down
if /i "%COMMAND%"=="logs" goto :logs
if /i "%COMMAND%"=="shell" goto :shell
if /i "%COMMAND%"=="migrate" goto :migrate
if /i "%COMMAND%"=="ui-dev" goto :ui-dev

echo Unknown command: %COMMAND%
echo.
goto :help

:help
echo OpenMemory Windows Runner - Available commands:
echo.
echo   run-windows.bat help        - Show this help message
echo   run-windows.bat build       - Build the containers
echo   run-windows.bat up          - Start the containers
echo   run-windows.bat down        - Stop the containers
echo   run-windows.bat logs        - Show container logs
echo   run-windows.bat shell       - Open a shell in the api container
echo   run-windows.bat migrate     - Run database migrations
echo   run-windows.bat ui-dev      - Start the frontend in development mode
echo.
echo Environment variables (set before running):
echo   USER_ID    - Set the user ID (default: %USERNAME%)
echo   API_URL    - Set the API URL (default: http://localhost:8765)
echo.
echo Examples:
echo   run-windows.bat up
echo   set USER_ID=myuser && run-windows.bat up
goto :end

:build
echo Building containers...
docker compose build
goto :end

:up
echo Starting containers with:
echo   User ID: %NEXT_PUBLIC_USER_ID%
echo   API URL: %NEXT_PUBLIC_API_URL%
echo.
docker compose up
goto :end

:down
echo Stopping containers...
docker compose down -v
if exist "api\openmemory.db" (
    del "api\openmemory.db"
    echo Removed database file
)
goto :end

:logs
echo Showing container logs...
docker compose logs -f
goto :end

:shell
echo Opening shell in API container...
docker compose exec api bash
goto :end

:migrate
echo Running database migrations...
docker compose exec api alembic upgrade head
goto :end

:ui-dev
echo Starting UI in development mode...
echo   User ID: %NEXT_PUBLIC_USER_ID%
echo   API URL: %NEXT_PUBLIC_API_URL%
cd ui
pnpm install
pnpm dev
cd ..
goto :end

:end
endlocal
