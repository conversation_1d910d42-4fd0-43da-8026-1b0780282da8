---
title: Elasticsearch
---

Install related dependencies using the following command:

```bash
pip install --upgrade 'embedchain[elasticsearch]'
```

<Note>
You can configure the Elasticsearch connection by providing either `es_url` or `cloud_id`. If you are using the Elasticsearch Service on Elastic Cloud, you can find the `cloud_id` on the [Elastic Cloud dashboard](https://cloud.elastic.co/deployments).
</Note>

You can authorize the connection to Elasticsearch by providing either `basic_auth`, `api_key`, or `bearer_auth`.

<CodeGroup>

```python main.py
from embedchain import App

# load elasticsearch configuration from yaml file
app = App.from_config(config_path="config.yaml")
```

```yaml config.yaml
vectordb:
  provider: elasticsearch
  config:
    collection_name: 'es-index'
    cloud_id: 'deployment-name:xxxx'
    basic_auth:
      - elastic
      - <your_password>
    verify_certs: false
```
</CodeGroup>

<Snippet file="missing-vector-db-tip.mdx" />
