{"mem0": {"llm": {"provider": "gemini", "config": {"model": "gemini-1.5-flash", "temperature": 0.1, "max_tokens": 2000, "api_key": "env:GEMINI_API_KEY"}}, "embedder": {"provider": "gemini", "config": {"model": "models/text-embedding-004", "api_key": "env:GEMINI_API_KEY"}}, "vector_store": {"provider": "qdrant", "config": {"collection_name": "openmemory", "host": "mem0_store", "port": 6333, "embedding_model_dims": 768}}}}