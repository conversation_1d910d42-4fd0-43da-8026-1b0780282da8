---
title: Overview
description: 'Enhance your memory system with graph-based knowledge representation and retrieval'
icon: "database"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />

Mem0 now supports **Graph Memory**.
With Graph Memory, users can now create and utilize complex relationships between pieces of information, allowing for more nuanced and context-aware responses. 
This integration enables users to leverage the strengths of both vector-based and graph-based approaches, resulting in more accurate and comprehensive information retrieval and generation.

<Note>
NodeSDK now supports Graph Memory. 🎉
</Note>

## Installation

To use Mem0 with Graph Memory support, install it using pip:

<CodeGroup>
```bash Python
pip install "mem0ai[graph]"
```

```bash TypeScript
npm install mem0ai
```
</CodeGroup>

This command installs Mem0 along with the necessary dependencies for graph functionality.

Try Graph Memory on Google Colab.
<a target="_blank" href="https://colab.research.google.com/drive/1PfIGVHnliIlG2v8cx0g45TF0US-jRPZ1?usp=sharing">
  <img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open In Colab"/>
</a>


<iframe
width="100%"
height="400"
src="https://www.youtube.com/embed/u_ZAqNNVtXA"
title="YouTube video player"
frameborder="0"
allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
allowfullscreen
></iframe>

## Initialize Graph Memory

To initialize Graph Memory you'll need to set up your configuration with graph
store providers. Currently, we support [Neo4j](#initialize-neo4j) and
[Memgraph](#initialize-memgraph) as graph store providers. 


### Initialize Neo4j

You can setup [Neo4j](https://neo4j.com/) locally or use the hosted [Neo4j AuraDB](https://neo4j.com/product/auradb/).

<Note>If you are using Neo4j locally, then you need to install [APOC plugins](https://neo4j.com/labs/apoc/4.1/installation/).</Note>

User can also customize the LLM for Graph Memory from the [Supported LLM list](https://docs.mem0.ai/components/llms/overview) with three levels of configuration:

1. **Main Configuration**: If `llm` is set in the main config, it will be used for all graph operations.
2. **Graph Store Configuration**: If `llm` is set in the graph_store config, it will override the main config `llm` and be used specifically for graph operations.
3. **Default Configuration**: If no custom LLM is set, the default LLM (`gpt-4o-2024-08-06`) will be used for all graph operations.

Here's how you can do it:


<CodeGroup>
```python Python
from mem0 import Memory

config = {
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": "neo4j+s://xxx",
            "username": "neo4j",
            "password": "xxx"
        }
    }
}

m = Memory.from_config(config_dict=config)
```

```typescript TypeScript
import { Memory } from "mem0ai/oss";

const config = {
    enableGraph: true,
    graphStore: {
        provider: "neo4j",
        config: {
            url: "neo4j+s://xxx",
            username: "neo4j",
            password: "xxx",
        }
    }
}

const memory = new Memory(config);
```

```python Python (Advanced)
config = {
    "llm": {
        "provider": "openai",
        "config": {
            "model": "gpt-4o",
            "temperature": 0.2,
            "max_tokens": 2000,
        }
    },
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": "neo4j+s://xxx",
            "username": "neo4j",
            "password": "xxx"
        },
        "llm" : {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.0,
            }
        }
    }
}

m = Memory.from_config(config_dict=config)
```

```typescript TypeScript (Advanced)
const config = {
    llm: {
        provider: "openai",
        config: {
            model: "gpt-4o",
            temperature: 0.2,
            max_tokens: 2000,
        }
    },
    enableGraph: true,
    graphStore: {
        provider: "neo4j",
        config: {
            url: "neo4j+s://xxx",
            username: "neo4j",
            password: "xxx",
        },
        llm: {
            provider: "openai",
            config: {
                model: "gpt-4o-mini",
                temperature: 0.0,
            }
        }
    }
}

const memory = new Memory(config);
```
</CodeGroup>

<Note>
If you are using NodeSDK, you need to pass `enableGraph` as `true` in the `config` object.
</Note>

### Initialize Memgraph

Run Memgraph with Docker:

```bash
docker run -p 7687:7687 memgraph/memgraph-mage:latest --schema-info-enabled=True
```

The `--schema-info-enabled` flag is set to `True` for more performant schema
generation.

Additional information can be found on [Memgraph
documentation](https://memgraph.com/docs). 

User can also customize the LLM for Graph Memory from the [Supported LLM list](https://docs.mem0.ai/components/llms/overview) with three levels of configuration:

1. **Main Configuration**: If `llm` is set in the main config, it will be used for all graph operations.
2. **Graph Store Configuration**: If `llm` is set in the graph_store config, it will override the main config `llm` and be used specifically for graph operations.
3. **Default Configuration**: If no custom LLM is set, the default LLM (`gpt-4o-2024-08-06`) will be used for all graph operations.

Here's how you can do it:


<CodeGroup>
```python Python
from mem0 import Memory

config = {
    "graph_store": {
        "provider": "memgraph",
        "config": {
            "url": "bolt://localhost:7687",
            "username": "memgraph",
            "password": "xxx",
        },
    },
}

m = Memory.from_config(config_dict=config)
```

```python Python (Advanced)
config = {
    "embedder": {
        "provider": "openai",
        "config": {"model": "text-embedding-3-large", "embedding_dims": 1536},
    },
    "graph_store": {
        "provider": "memgraph",
        "config": {
            "url": "bolt://localhost:7687",
            "username": "memgraph",
            "password": "xxx"
        }
    }
}

m = Memory.from_config(config_dict=config)
```
</CodeGroup>

## Graph Operations
The Mem0's graph supports the following operations:

### Add Memories

<Note>
If you are using Mem0 with Graph Memory, it is recommended to pass `user_id`. Use `userId` in NodeSDK.
</Note>

<CodeGroup>
```python Python
m.add("I like pizza", user_id="alice")
```

```typescript TypeScript
memory.add("I like pizza", { userId: "alice" });
```

```json Output
{'message': 'ok'}
```
</CodeGroup>


### Get all memories

<CodeGroup>
```python Python
m.get_all(user_id="alice")
```

```typescript TypeScript
memory.getAll({ userId: "alice" });
```

```json Output
{
    'memories': [
        {
            'id': 'de69f426-0350-4101-9d0e-5055e34976a5',
            'memory': 'Likes pizza',
            'hash': '92128989705eef03ce31c462e198b47d',
            'metadata': None,
            'created_at': '2024-08-20T14:09:27.588719-07:00',
            'updated_at': None,
            'user_id': 'alice'
        }
    ],
    'entities': [
        {
            'source': 'alice',
            'relationship': 'likes',
            'target': 'pizza'
        }
    ]
}
```
</CodeGroup>

### Search Memories

<CodeGroup>
```python Python
m.search("tell me my name.", user_id="alice")
```

```typescript TypeScript
memory.search("tell me my name.", { userId: "alice" });
```

```json Output
{
    'memories': [
        {
            'id': 'de69f426-0350-4101-9d0e-5055e34976a5',
            'memory': 'Likes pizza',
            'hash': '92128989705eef03ce31c462e198b47d',
            'metadata': None,
            'created_at': '2024-08-20T14:09:27.588719-07:00',
            'updated_at': None,
            'user_id': 'alice'
        }
    ],
    'entities': [
        {
            'source': 'alice',
            'relationship': 'likes',
            'target': 'pizza'
        }
    ]
}
```
</CodeGroup>


### Delete all Memories

<CodeGroup>
```python Python
m.delete_all(user_id="alice")
```

```typescript TypeScript
memory.deleteAll({ userId: "alice" });
```
</CodeGroup>

# Example Usage
Here's an example of how to use Mem0's graph operations:

1. First, we'll add some memories for a user named Alice.
2. Then, we'll visualize how the graph evolves as we add more memories.
3. You'll see how entities and relationships are automatically extracted and connected in the graph.

### Add Memories

Below are the steps to add memories and visualize the graph:

<Steps>
  <Step title="Add memory 'I like going to hikes'">

<CodeGroup>
```python Python
m.add("I like going to hikes", user_id="alice123")
```

```typescript TypeScript
memory.add("I like going to hikes", { userId: "alice123" });
```
</CodeGroup>
![Graph Memory Visualization](/images/graph_memory/graph_example1.png)

</Step>
<Step title="Add memory 'I love to play badminton'">

<CodeGroup>
```python Python
m.add("I love to play badminton", user_id="alice123")
```

```typescript TypeScript
memory.add("I love to play badminton", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example2.png)

</Step>

<Step title="Add memory 'I hate playing badminton'">

<CodeGroup>
```python Python
m.add("I hate playing badminton", user_id="alice123")
```

```typescript TypeScript
memory.add("I hate playing badminton", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example3.png)

</Step>

<Step title="Add memory 'My friend name is john and john has a dog named tommy'">

<CodeGroup>
```python Python
m.add("My friend name is john and john has a dog named tommy", user_id="alice123")
```

```typescript TypeScript
memory.add("My friend name is john and john has a dog named tommy", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example4.png)

</Step>

<Step title="Add memory 'My name is Alice'">

<CodeGroup>
```python Python
m.add("My name is Alice", user_id="alice123")
```

```typescript TypeScript
memory.add("My name is Alice", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example5.png)

</Step>

<Step title="Add memory 'John loves to hike and Harry loves to hike as well'">

<CodeGroup>
```python Python
m.add("John loves to hike and Harry loves to hike as well", user_id="alice123")
```

```typescript TypeScript
memory.add("John loves to hike and Harry loves to hike as well", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example6.png)

</Step>

<Step title="Add memory 'My friend peter is the spiderman'">

<CodeGroup>
```python Python
m.add("My friend peter is the spiderman", user_id="alice123")
```

```typescript TypeScript
memory.add("My friend peter is the spiderman", { userId: "alice123" });
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example7.png)

</Step>

</Steps>


### Search Memories

<CodeGroup>
```python Python
m.search("What is my name?", user_id="alice123")
```

```typescript TypeScript
memory.search("What is my name?", { userId: "alice123" });
```

```json Output
{
    'memories': [...],
    'entities': [
        {'source': 'alice123', 'relation': 'dislikes_playing','destination': 'badminton'},
        {'source': 'alice123', 'relation': 'friend', 'destination': 'peter'},
        {'source': 'alice123', 'relation': 'friend', 'destination': 'john'},
        {'source': 'alice123', 'relation': 'has_name', 'destination': 'alice'},
        {'source': 'alice123', 'relation': 'likes', 'destination': 'hiking'}
    ]
}
```
</CodeGroup>

Below graph visualization shows what nodes and relationships are fetched from the graph for the provided query.

![Graph Memory Visualization](/images/graph_memory/graph_example8.png)

<CodeGroup>
```python Python
m.search("Who is spiderman?", user_id="alice123")
```

```typescript TypeScript
memory.search("Who is spiderman?", { userId: "alice123" });
```

```json Output
{
    'memories': [...],
    'entities': [
        {'source': 'peter', 'relation': 'identity','destination': 'spiderman'}
    ]
}
```
</CodeGroup>

![Graph Memory Visualization](/images/graph_memory/graph_example9.png)

> **Note:** The Graph Memory implementation is not standalone. You will be adding/retrieving memories to the vector store and the graph store simultaneously.

If you want to use a managed version of Mem0, please check out [Mem0](https://mem0.dev/pd). If you have any questions, please feel free to reach out to us using one of the following methods:

<Snippet file="get-help.mdx" />