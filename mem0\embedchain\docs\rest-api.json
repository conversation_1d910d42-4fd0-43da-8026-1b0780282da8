{"openapi": "3.1.0", "info": {"title": "Embedchain REST API", "description": "This is the REST API for Embedchain.", "license": {"name": "Apache 2.0", "url": "https://github.com/embedchain/embedchain/blob/main/LICENSE"}, "version": "0.0.1"}, "paths": {"/ping": {"get": {"tags": ["Utility"], "summary": "Check status", "description": "Endpoint to check the status of the API", "operationId": "check_status_ping_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/apps": {"get": {"tags": ["Apps"], "summary": "Get all apps", "description": "Get all applications", "operationId": "get_all_apps_apps_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/create": {"post": {"tags": ["Apps"], "summary": "Create app", "description": "Create a new app using App ID", "operationId": "create_app_using_default_config_create_post", "parameters": [{"name": "app_id", "in": "query", "required": true, "schema": {"type": "string", "title": "App Id"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"allOf": [{"$ref": "#/components/schemas/Body_create_app_using_default_config_create_post"}], "title": "Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/data": {"get": {"tags": ["Apps"], "summary": "Get data", "description": "Get all data sources for an app", "operationId": "get_datasources_associated_with_app_id__app_id__data_get", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/add": {"post": {"tags": ["Apps"], "summary": "Add data", "description": "Add a data source to an app.", "operationId": "add_datasource_to_an_app__app_id__add_post", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceApp"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/query": {"post": {"tags": ["Apps"], "summary": "Query app", "description": "Query an app", "operationId": "query_an_app__app_id__query_post", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryApp"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/chat": {"post": {"tags": ["Apps"], "summary": "Cha<PERSON>", "description": "Chat with an app.\n\napp_id: The ID of the app. Use \"default\" for the default app.\n\nmessage: The message that you want to send to the app.", "operationId": "chat_with_an_app__app_id__chat_post", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageApp"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/deploy": {"post": {"tags": ["Apps"], "summary": "Deploy app", "description": "Deploy an existing app.", "operationId": "deploy_app__app_id__deploy_post", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeployAppRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/{app_id}/delete": {"delete": {"tags": ["Apps"], "summary": "Delete app", "description": "Delete an existing app", "operationId": "delete_app__app_id__delete_delete", "parameters": [{"name": "app_id", "in": "path", "required": true, "schema": {"type": "string", "title": "App Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"Body_create_app_using_default_config_create_post": {"properties": {"config": {"type": "string", "format": "binary", "title": "Config"}}, "type": "object", "title": "Body_create_app_using_default_config_create_post"}, "DefaultResponse": {"properties": {"response": {"type": "string", "title": "Response"}}, "type": "object", "required": ["response"], "title": "DefaultResponse"}, "DeployAppRequest": {"properties": {"api_key": {"type": "string", "title": "Api Key", "description": "The Embedchain API key for app deployments. You get the api key on the Embedchain platform by visiting [https://app.embedchain.ai](https://app.embedchain.ai)", "default": ""}}, "type": "object", "title": "DeployAppRequest", "example": {"api_key": "ec-xxx"}}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MessageApp": {"properties": {"message": {"type": "string", "title": "Message", "description": "The message that you want to send to the App.", "default": ""}}, "type": "object", "title": "MessageApp"}, "QueryApp": {"properties": {"query": {"type": "string", "title": "Query", "description": "The query that you want to ask the App.", "default": ""}}, "type": "object", "title": "QueryApp", "example": {"query": "Who is <PERSON><PERSON>?"}}, "SourceApp": {"properties": {"source": {"type": "string", "title": "Source", "description": "The source that you want to add to the App.", "default": ""}, "data_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Data Type", "description": "The type of data to add, remove it if you want Embedchain to detect it automatically.", "default": ""}}, "type": "object", "title": "SourceApp", "example": {"source": "https://en.wikipedia.org/wiki/Elon_Musk"}}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}