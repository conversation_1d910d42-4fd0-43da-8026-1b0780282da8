---
title: Document Editing with Mem0
---
<Snippet file="paper-release.mdx" />

This guide demonstrates how to leverage **Mem0** to edit documents efficiently, ensuring they align with your unique writing style and preferences.

## **Why Use Mem0?**

By integrating Mem0 into your workflow, you can streamline your document editing process with:

1. **Persistent Writing Preferences**: Mem0 stores and recalls your style preferences, ensuring consistency across all documents.
2. **Automated Enhancements**: Your stored preferences guide document refinements, making edits seamless and efficient.
3. **Scalability & Reusability**: Your writing style can be applied to multiple documents, saving time and effort.

---
## **Setup**

```python
import os
from mem0 import MemoryClient

# Set up Mem0 client
os.environ["MEM0_API_KEY"] = "your-mem0-api-key"
client = MemoryClient()

# Define constants
USER_ID = "content_writer"
RUN_ID = "smart_editing_session"
```

---
## **Storing Your Writing Preferences in Mem0**

```python
def store_writing_preferences():
    """Store your writing preferences in Mem0."""
    
    # Define writing preferences
    preferences = """My writing preferences:
1. Use headings and sub-headings for structure.
2. Keep paragraphs concise (8-10 sentences max).
3. Incorporate specific numbers and statistics.
4. Provide concrete examples.
5. Use bullet points for clarity.
6. Avoid jargon and buzzwords."""

    # Store preferences in Mem0
    preference_message = [
        {"role": "user", "content": "Here are my writing style preferences"},
        {"role": "assistant", "content": preferences}
    ]
    
    response = client.add(preference_message, user_id=USER_ID, run_id=RUN_ID, metadata={"type": "preferences", "category": "writing_style"})
    
    print("Writing preferences stored successfully.")
    return response
```

---
## **Editing Documents with Mem0**

```python
def edit_document_based_on_preferences(original_content):
    """Edit a document using Mem0-based stored preferences."""
    
    # Retrieve stored preferences
    query = "What are my writing style preferences?"
    preferences_results = client.search(query, user_id=USER_ID, run_id=RUN_ID)
    
    if not preferences_results:
        print("No writing preferences found.")
        return None
    
    # Extract preferences
    preferences = ' '.join(memory["memory"] for memory in preferences_results)
    
    # Apply stored preferences to refine the document
    edited_content = f"Applying stored preferences:\n{preferences}\n\nEdited Document:\n{original_content}"
    
    return edited_content
```

---
## **Complete Workflow: Document Editing**

```python
def document_editing_workflow(content):
    """Automated workflow for editing a document based on writing preferences."""
    
    # Step 1: Store writing preferences (if not already stored)
    store_writing_preferences()
    
    # Step 2: Edit the document with Mem0 preferences
    edited_content = edit_document_based_on_preferences(content)
    
    if not edited_content:
        return "Failed to edit document."
    
    # Step 3: Display results
    print("\n=== ORIGINAL DOCUMENT ===\n")
    print(content)
    
    print("\n=== EDITED DOCUMENT ===\n")
    print(edited_content)
    
    return edited_content
```

---
## **Example Usage**

```python
# Define your document
original_content = """Project Proposal
    
The following proposal outlines our strategy for the Q3 marketing campaign. 
We believe this approach will significantly increase our market share.

Increase brand awareness
Boost sales by 15%
Expand our social media following

We plan to launch the campaign in July and continue through September.
"""

# Run the workflow
result = document_editing_workflow(original_content)
```

---
## **Expected Output**

Your document will be transformed into a structured, well-formatted version based on your preferences.

### **Original Document**
```
Project Proposal
    
The following proposal outlines our strategy for the Q3 marketing campaign. 
We believe this approach will significantly increase our market share.

Increase brand awareness
Boost sales by 15%
Expand our social media following

We plan to launch the campaign in July and continue through September.
```

### **Edited Document**
```
# **Project Proposal**

## **Q3 Marketing Campaign Strategy**

This proposal outlines our strategy for the Q3 marketing campaign. We aim to significantly increase our market share with this approach.

### **Objectives**

- **Increase Brand Awareness**: Implement targeted advertising and community engagement to enhance visibility.
- **Boost Sales by 15%**: Increase sales by 15% compared to Q2 figures.
- **Expand Social Media Following**: Grow our social media audience by 20%.

### **Timeline**

- **Launch Date**: July
- **Duration**: July – September

### **Key Actions**

- **Targeted Advertising**: Utilize platforms like Google Ads and Facebook to reach specific demographics.
- **Community Engagement**: Host webinars and live Q&A sessions.
- **Content Creation**: Produce engaging videos and infographics.

### **Supporting Data**

- **Previous Campaign Success**: Our Q2 campaign increased sales by 12%. We will refine similar strategies for Q3.
- **Social Media Growth**: Last year, our Instagram followers grew by 25% during a similar campaign.

### **Conclusion**

We believe this strategy will effectively increase our market share. To achieve these goals, we need your support and collaboration. Let’s work together to make this campaign a success. Please review the proposal and provide your feedback by the end of the week.
```

Mem0 creates a seamless, intelligent document editing experience—perfect for content creators, technical writers, and businesses alike!