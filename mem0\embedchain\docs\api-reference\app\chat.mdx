---
title: '💬 chat'
---

`chat()` method allows you to chat over your data sources using a user-friendly chat API. You can find the signature below:

### Parameters

<ParamField path="input_query" type="str">
    Question to ask
</ParamField>
<ParamField path="config" type="BaseLlmConfig" optional>
    Configure different llm settings such as prompt, temprature, number_documents etc.
</ParamField>
<ParamField path="dry_run" type="bool" optional>
    The purpose is to test the prompt structure without actually running LLM inference. Defaults to `False`
</ParamField>
<ParamField path="where" type="dict" optional>
    A dictionary of key-value pairs to filter the chunks from the vector database. Defaults to `None`
</ParamField>
<ParamField path="session_id" type="str" optional>
    Session ID of the chat. This can be used to maintain chat history of different user sessions. Default value: `default`
</ParamField>
<ParamField path="citations" type="bool" optional>
    Return citations along with the LLM answer. Defaults to `False`
</ParamField>

### Returns

<ResponseField name="answer" type="str | tuple">
  If `citations=False`, return a stringified answer to the question asked. <br />
  If `citations=True`, returns a tuple with answer and citations respectively.
</ResponseField>

## Usage

### With citations

If you want to get the answer to question and return both answer and citations, use the following code snippet:

```python With Citations
from embedchain import App

# Initialize app
app = App()

# Add data source
app.add("https://www.forbes.com/profile/elon-musk")

# Get relevant answer for your query
answer, sources = app.chat("What is the net worth of Elon?", citations=True)
print(answer)
# Answer: The net worth of Elon Musk is $221.9 billion.

print(sources)
# [
#    (
#        'Elon Musk PROFILEElon MuskCEO, Tesla$247.1B$2.3B (0.96%)Real Time Net Worthas of 12/7/23 ...',
#        {
#           'url': 'https://www.forbes.com/profile/elon-musk', 
#           'score': 0.89,
#           ...
#        }
#    ),
#    (
#        '74% of the company, which is now called X.Wealth HistoryHOVER TO REVEAL NET WORTH BY YEARForbes ...',
#        {
#           'url': 'https://www.forbes.com/profile/elon-musk', 
#           'score': 0.81,
#           ...
#        }
#    ),
#    (
#        'founded in 2002, is worth nearly $150 billion after a $750 million tender offer in June 2023 ...',
#        {
#           'url': 'https://www.forbes.com/profile/elon-musk', 
#           'score': 0.73,
#           ...
#        }
#    )
# ]
```

<Note>
When `citations=True`, note that the returned `sources` are a list of tuples where each tuple has two elements (in the following order):
1. source chunk
2. dictionary with metadata about the source chunk
    - `url`: url of the source
    - `doc_id`: document id (used for book keeping purposes)
    - `score`: score of the source chunk with respect to the question
    - other metadata you might have added at the time of adding the source
</Note>


### Without citations

If you just want to return answers and don't want to return citations, you can use the following example:

```python Without Citations
from embedchain import App

# Initialize app
app = App()

# Add data source
app.add("https://www.forbes.com/profile/elon-musk")

# Chat on your data using `.chat()`
answer = app.chat("What is the net worth of Elon?")
print(answer)
# Answer: The net worth of Elon Musk is $221.9 billion.
```

### With session id

If you want to maintain chat sessions for different users, you can simply pass the `session_id` keyword argument. See the example below:

```python With session id
from embedchain import App

app = App()
app.add("https://www.forbes.com/profile/elon-musk")

# Chat on your data using `.chat()`
app.chat("What is the net worth of Elon Musk?", session_id="user1")
# 'The net worth of Elon Musk is $250.8 billion.'
app.chat("What is the net worth of Bill Gates?", session_id="user2")
# "I don't know the current net worth of Bill Gates."
app.chat("What was my last question", session_id="user1")
# 'Your last question was "What is the net worth of Elon Musk?"'
```

### With custom context window

If you want to customize the context window that you want to use during chat (default context window is 3 document chunks), you can do using the following code snippet:

```python with custom chunks size
from embedchain import App
from embedchain.config import BaseLlmConfig

app = App()
app.add("https://www.forbes.com/profile/elon-musk")

query_config = BaseLlmConfig(number_documents=5)
app.chat("What is the net worth of Elon Musk?", config=query_config)
```

### With Mem0 to store chat history

Mem0 is a cutting-edge long-term memory for LLMs to enable personalization for the GenAI stack. It enables LLMs to remember past interactions and provide more personalized responses. 

In order to use Mem0 to enable memory for personalization in your apps:
- Install the [`mem0`](https://docs.mem0.ai/) package using `pip install mem0ai`. 
- Prepare config for `memory`, refer [Configurations](docs/api-reference/advanced/configuration.mdx).

```python with mem0
from embedchain import App

config = {
  "memory": {
    "top_k": 5
  }
}

app = App.from_config(config=config)
app.add("https://www.forbes.com/profile/elon-musk")

app.chat("What is the net worth of Elon Musk?")
```

## How Mem0 works:
- Mem0 saves context derived from each user question into its memory.
- When a user poses a new question, Mem0 retrieves relevant previous memories.
- The `top_k` parameter in the memory configuration specifies the number of top memories to consider during retrieval.
- Mem0 generates the final response by integrating the user's question, context from the data source, and the relevant memories.
